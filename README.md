# H5跳转微信小程序功能

## 项目说明

本项目实现了从H5页面跳转到微信小程序的功能，专为微信小程序的web-view组件设计。

## 文件说明

- `index.html` - H5跳转页面（完整功能）
- `README.md` - 项目说明文档

## 配置信息

- **小程序AppID**: `wx134ad5c5b71a7283`
- **跳转路径**: `/pages/mine/mine`
- **页面标题**: "熵量云图调解"

## 使用步骤

### 1. 部署H5页面
将 `index.html` 文件部署到您的服务器上，确保域名已在小程序管理后台配置为业务域名。

### 2. 小程序集成
在小程序中使用web-view组件加载H5页面：

```xml
<web-view src="https://yourdomain.com/index.html"></web-view>
```

## 功能特性

- ✅ 微信环境自动检测
- ✅ 小程序web-view环境检测
- ✅ 响应式设计，适配移动端
- ✅ 完整的错误处理机制
- ✅ 加载状态显示
- ✅ 现代化UI设计

## 注意事项

1. **域名配置**: 必须在小程序管理后台配置业务域名白名单
2. **HTTPS要求**: 生产环境必须使用HTTPS协议
3. **环境限制**: 跳转功能仅在微信小程序web-view中有效
4. **无需签名**: 小程序web-view环境无需配置微信JS-SDK签名

## 技术支持

如有问题，请检查：
1. 域名是否已正确配置
2. 小程序AppID是否正确
3. 跳转路径是否存在
4. 是否在小程序web-view环境中测试
