<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>返回小程序</title>
    <script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <!-- WeUI 样式框架 -->
    <link rel="stylesheet" href="https://res.wx.qq.com/open/libs/weui/2.4.1/weui.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f8f8f8;
            min-height: 100vh;
        }

        .page {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            min-height: 100vh;
            padding: 18px;
            padding-top: 120px; /* 调整到页面中上方 */
        }

        /* 顶部成功图标 - 复刻微信小程序样式 */
        .success-icon {
            width: 115px; /* 增大到115px提升醒目度 */
            height: 115px;
            background-color: #09BB07; /* 微信成功绿色 */
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40px; /* 调整间距为40px */
            position: relative;
            /* 微信小程序成功图标的特殊样式 */
            border: 3px solid #09BB07;
            box-shadow: 0 0 0 1px rgba(9, 187, 7, 0.1), 0 2px 8px rgba(9, 187, 7, 0.2);
        }

        /* 添加微信小程序成功图标的内圈效果 */
        .success-icon::before {
            content: "";
            position: absolute;
            width: 107px; /* 按比例调整内圈大小 */
            height: 107px;
            background-color: #09BB07;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1;
        }

        /* 复刻微信小程序成功图标的勾选符号 */
        .success-icon::after {
            content: "";
            position: absolute;
            left: 50%;
            top: 50%;
            width: 22px; /* 按比例调整勾选符号大小 */
            height: 40px;
            border: solid white;
            border-width: 0 5px 5px 0;
            transform: translate(-50%, -60%) rotate(45deg);
            z-index: 2; /* 确保勾选符号显示在内圈之上 */
        }

        /* 提示文字 */
        .tip-text {
            font-size: 21px; /* 增大到21px提升醒目度 */
            color: #333333;
            text-align: center;
            margin-bottom: 40px; /* 调整间距为40px */
            line-height: 1.4;
        }

        /* 操作按钮 */
        .return-btn {
            background-color: #4293F4;
            color: #FFFFFF;
            border: none;
            border-radius: 8px;
            padding: 14px 24px; /* 调整高度与message-container一致 */
            font-size: 19px; /* 增大到19px提升醒目度 */
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(66, 147, 244, 0.3);
            width: calc(100vw - 36px); /* 与message-container宽度一致 */
            max-width: calc(100vw - 36px);
            line-height: 1.4; /* 与消息元素行高一致 */
        }

        .return-btn:hover {
            background-color: #3182ce;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(66, 147, 244, 0.4);
        }

        .return-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(66, 147, 244, 0.3);
        }

        .return-btn:disabled {
            background-color: #c8c8c8;
            color: #999;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 加载状态 */
        .loading-container {
            display: none;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            color: #888;
            font-size: 14px;
        }

        .weui-loading {
            width: 20px;
            height: 20px;
            margin-right: 10px;
        }

        /* 消息提示 */
        .message-container {
            position: fixed;
            top: 20px;
            left: 18px;
            right: 18px;
            z-index: 1000;
        }

        .error-message,
        .success-message,
        .env-info {
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 8px;
            display: none;
            text-align: center;
        }

        .error-message {
            background: #fef0f0;
            border: 1px solid #fbc4c4;
            color: #f56c6c;
        }

        .success-message {
            background: #f0f9ff;
            border: 1px solid #b3d8ff;
            color: #409eff;
        }

        .env-info {
            background: #f7f7f7;
            border: 1px solid #e0e0e0;
            color: #666;
            font-size: 13px;
        }

        /* 调试面板 */
        .debug-panel {
            position: fixed;
            bottom: 60px;
            left: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 9999;
        }

        .debug-panel h4 {
            margin: 0 0 10px 0;
            color: #fff;
        }

        .debug-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #ccc;
            border: none;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .page {
                padding: 16px;
                padding-top: 100px; /* 移动端调整顶部间距 */
            }

            .success-icon {
                width: 92px; /* 增大到92px提升醒目度 */
                height: 92px;
                margin-bottom: 32px; /* 调整间距为32px */
                border-width: 2px; /* 调整边框宽度 */
            }

            .success-icon::before {
                width: 86px; /* 按比例调整内圈大小 */
                height: 86px;
            }

            .success-icon::after {
                width: 18px; /* 按比例调整勾选符号 */
                height: 32px;
                border-width: 0 4px 4px 0;
            }

            .tip-text {
                font-size: 18px; /* 增大到18px提升醒目度 */
                margin-bottom: 32px; /* 调整间距为32px */
            }

            .return-btn {
                font-size: 16px; /* 增大到16px提升醒目度 */
                padding: 12px 22px; /* 调整高度与message-container一致 */
                width: calc(100vw - 32px); /* 与message-container宽度一致 */
                max-width: calc(100vw - 32px);
            }

            .message-container {
                left: 16px;
                right: 16px;
            }
        }

        @media (max-width: 360px) {
            .page {
                padding-top: 80px; /* 小屏幕进一步调整 */
            }

            .success-icon {
                width: 87px; /* 增大到87px提升醒目度 */
                height: 87px;
                border-width: 2px; /* 调整边框宽度 */
            }

            .success-icon::before {
                width: 81px; /* 按比例调整内圈大小 */
                height: 81px;
            }

            .success-icon::after {
                width: 16px; /* 按比例调整勾选符号 */
                height: 28px;
                border-width: 0 3px 3px 0;
            }

            .tip-text {
                font-size: 17px; /* 增大到17px提升醒目度 */
            }

            .return-btn {
                font-size: 15px; /* 增大到15px提升醒目度 */
                padding: 11px 20px; /* 调整高度与message-container一致 */
            }
        }
    </style>
</head>

<body>
    <!-- 消息提示区域 -->
    <div class="message-container">
        <div id="errorMessage" class="error-message"></div>
        <div id="successMessage" class="success-message"></div>
        <div id="envInfo" class="env-info"></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page">
        <!-- 顶部成功图标 -->
        <div class="success-icon"></div>

        <!-- 提示文字 -->
        <p class="tip-text">点击下方按钮即刻返回</p>

        <!-- 操作按钮 -->
        <button id="jumpBtn" class="return-btn" onclick="jumpToMiniProgram()">
            返回熵量云图调解
        </button>

        <!-- 加载状态 -->
        <div id="loading" class="loading-container">
            <div class="weui-loading"></div>
            正在跳转...
        </div>
    </div>

    <!-- 调试信息面板 -->
    <div id="debugPanel" class="debug-panel" style="display: none;">
        <h4>调试信息</h4>
        <div id="debugInfo"></div>
        <button onclick="toggleDebug()" style="margin-top: 10px;">关闭调试</button>
    </div>

    <!-- 调试按钮 -->
    <button onclick="toggleDebug()" class="debug-btn">调试</button>

    <script>
        // 小程序配置信息
        const MINI_PROGRAM_CONFIG = {
            appId: 'wx134ad5c5b71a7283',
            path: '/pages/mine/mine'
        };

        // 全局状态
        let isInMiniProgram = false;
        let isWeixinBridgeReady = false;
        let deviceInfo = {
            isIOS: false,
            isAndroid: false,
            wechatVersion: '',
            userAgent: ''
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            initPage();
        });

        // 初始化页面
        function initPage() {
            console.log('页面初始化开始');
            detectDeviceInfo();
            checkEnvironment();
            setupEventListeners();
            waitForWeixinBridge();
        }

        // 检测设备信息
        function detectDeviceInfo() {
            const userAgent = navigator.userAgent;
            deviceInfo.userAgent = userAgent;
            deviceInfo.isIOS = /iPhone|iPad|iPod/i.test(userAgent);
            deviceInfo.isAndroid = /Android/i.test(userAgent);

            // 提取微信版本号
            const wechatMatch = userAgent.match(/MicroMessenger\/([0-9.]+)/);
            if (wechatMatch) {
                deviceInfo.wechatVersion = wechatMatch[1];
            }

            console.log('设备信息:', deviceInfo);
        }

        // 等待微信JS桥接准备就绪
        function waitForWeixinBridge() {
            if (typeof WeixinJSBridge !== 'undefined') {
                console.log('WeixinJSBridge已准备就绪');
                isWeixinBridgeReady = true;
                onWeixinBridgeReady();
            } else {
                console.log('等待WeixinJSBridge准备就绪...');
                document.addEventListener('WeixinJSBridgeReady', function () {
                    console.log('WeixinJSBridge准备就绪事件触发');
                    isWeixinBridgeReady = true;
                    onWeixinBridgeReady();
                }, false);
            }
        }

        // 微信桥接准备就绪后的处理
        function onWeixinBridgeReady() {
            console.log('开始进行小程序环境检测');
            checkMiniProgramEnvironment();
        }

        // 检测运行环境
        function checkEnvironment() {
            const userAgent = navigator.userAgent;
            console.log('UserAgent:', userAgent);

            // 检测是否在微信环境中
            if (userAgent.indexOf('MicroMessenger') === -1) {
                console.log('不在微信环境中');
                showError('请在微信中打开此页面');
                document.getElementById('jumpBtn').disabled = true;
                return false;
            }

            console.log('在微信环境中');
            return true;
        }

        // 检测小程序环境
        function checkMiniProgramEnvironment() {
            const userAgent = navigator.userAgent;

            // 方法1: 检查window.__wxjs_environment
            if (window.__wxjs_environment === 'miniprogram') {
                console.log('通过__wxjs_environment检测到小程序环境');
                isInMiniProgram = true;
                showEnvInfo('✅ 小程序环境检测成功 (方法1)');
                enableJumpButton();
                return;
            }

            // 方法2: 检查userAgent中的miniProgram标识
            if (userAgent.indexOf('miniProgram') !== -1) {
                console.log('通过userAgent检测到小程序环境');
                isInMiniProgram = true;
                showEnvInfo('✅ 小程序环境检测成功 (方法2)');
                enableJumpButton();
                return;
            }

            // 方法3: 使用wx.miniProgram.getEnv
            if (typeof wx !== 'undefined' && wx.miniProgram && wx.miniProgram.getEnv) {
                console.log('使用wx.miniProgram.getEnv检测环境');
                wx.miniProgram.getEnv(function (res) {
                    console.log('getEnv结果:', res);
                    if (res.miniprogram) {
                        console.log('通过getEnv检测到小程序环境');
                        isInMiniProgram = true;
                        showEnvInfo('✅ 小程序环境检测成功 (方法3)');
                        enableJumpButton();
                    } else {
                        console.log('getEnv检测结果：不在小程序环境');
                        handleNotInMiniProgram();
                    }
                });
            } else {
                console.log('wx.miniProgram.getEnv不可用');
                handleNotInMiniProgram();
            }
        }

        // 处理不在小程序环境的情况
        function handleNotInMiniProgram() {
            console.log('未检测到小程序环境');
            showError('请在小程序中打开此页面');
            document.getElementById('jumpBtn').disabled = true;
        }

        // 启用跳转按钮
        function enableJumpButton() {
            console.log('启用跳转按钮');
            const jumpBtn = document.getElementById('jumpBtn');
            jumpBtn.disabled = false;
            jumpBtn.style.opacity = '1';
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 监听页面状态变化（微信7.0.3+）
            if (typeof WeixinJSBridge !== 'undefined') {
                WeixinJSBridge.on('onPageStateChange', function (res) {
                    console.log('页面状态:', res.active ? '前台' : '后台');
                });
            }
        }

        // 跳转到小程序（简化版）
        function jumpToMiniProgram() {
            console.log('开始跳转到小程序');
            const jumpBtn = document.getElementById('jumpBtn');
            const loading = document.getElementById('loading');

            // 检查环境状态
            if (!isInMiniProgram) {
                console.log('不在小程序环境中，无法跳转');
                showError('当前不在小程序环境中');
                return;
            }

            if (!isWeixinBridgeReady) {
                console.log('微信桥接未准备就绪');
                showError('微信环境未准备就绪，请稍后重试');
                return;
            }

            // 显示加载状态
            jumpBtn.style.display = 'none';
            loading.style.display = 'flex';
            hideMessages();

            console.log('使用简化的两步跳转策略');
            console.log('设备信息:', deviceInfo);

            // 简化的两步跳转策略
            executeSimpleJump();
        }

        // 执行简化的跳转逻辑
        function executeSimpleJump() {
            console.log('执行简化跳转：第一步 - navigateBack');

            // 第一步：尝试 navigateBack
            try {
                if (typeof wx === 'undefined' || !wx.miniProgram || !wx.miniProgram.navigateBack) {
                    console.log('navigateBack不可用，直接尝试redirectTo');
                    tryRedirectToFallback();
                    return;
                }

                wx.miniProgram.navigateBack({
                    delta: 1,
                    success: function (res) {
                        console.log('navigateBack成功:', res);
                        showSuccess('返回成功 (navigateBack)');
                        setTimeout(resetButton, 1500);
                    },
                    fail: function (err) {
                        console.log('navigateBack失败:', err);
                        console.log('执行简化跳转：第二步 - redirectTo');
                        tryRedirectToFallback();
                    }
                });
            } catch (error) {
                console.error('navigateBack异常:', error);
                tryRedirectToFallback();
            }
        }

        // 备用方法：redirectTo
        function tryRedirectToFallback() {
            try {
                if (typeof wx === 'undefined' || !wx.miniProgram || !wx.miniProgram.redirectTo) {
                    console.log('redirectTo不可用');
                    showError('跳转功能不可用');
                    resetButton();
                    return;
                }

                const jumpUrl = MINI_PROGRAM_CONFIG.path + '?t=' + Date.now();
                console.log('尝试redirectTo，URL:', jumpUrl);

                wx.miniProgram.redirectTo({
                    url: jumpUrl,
                    success: function (res) {
                        console.log('redirectTo成功:', res);
                        showSuccess('跳转成功 (redirectTo)');
                        setTimeout(resetButton, 1500);
                    },
                    fail: function (err) {
                        console.error('redirectTo失败:', err);
                        showError('跳转失败：' + (err.errMsg || '未知错误'));
                        resetButton();
                    }
                });
            } catch (error) {
                console.error('redirectTo异常:', error);
                showError('跳转异常：' + error.message);
                resetButton();
            }
        }

        // 重置按钮状态
        function resetButton() {
            const jumpBtn = document.getElementById('jumpBtn');
            const loading = document.getElementById('loading');

            jumpBtn.style.display = 'block';
            loading.style.display = 'none';
        }

        // 显示错误信息
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // 显示成功信息
        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }

        // 显示环境信息
        function showEnvInfo(message) {
            const envDiv = document.getElementById('envInfo');
            envDiv.textContent = message;
            envDiv.style.display = 'block';
        }

        // 隐藏所有消息
        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }

        // 调试功能
        function toggleDebug() {
            const debugPanel = document.getElementById('debugPanel');
            const debugInfo = document.getElementById('debugInfo');

            if (debugPanel.style.display === 'none') {
                debugPanel.style.display = 'block';
                updateDebugInfo();
            } else {
                debugPanel.style.display = 'none';
            }
        }

        function updateDebugInfo() {
            const debugInfo = document.getElementById('debugInfo');

            // 检测跳转相关的API
            const apiAvailable = {};
            if (typeof wx !== 'undefined' && wx.miniProgram) {
                apiAvailable['navigateBack (主要)'] = !!wx.miniProgram.navigateBack;
                apiAvailable['redirectTo (备用)'] = !!wx.miniProgram.redirectTo;
                apiAvailable.getEnv = !!wx.miniProgram.getEnv;
            }

            const info = {
                '设备信息': deviceInfo,
                '小程序环境': isInMiniProgram,
                '微信桥接': isWeixinBridgeReady,
                '跳转路径': MINI_PROGRAM_CONFIG.path,
                'wx对象': typeof wx !== 'undefined',
                'wx.miniProgram': typeof wx !== 'undefined' && !!wx.miniProgram,
                'API可用性': apiAvailable,
                '页面可见性': document.visibilityState,
                'WeixinJSBridge': typeof WeixinJSBridge !== 'undefined',
                '__wxjs_environment': window.__wxjs_environment,
                '当前URL': window.location.href,
                '当前时间': new Date().toLocaleString()
            };

            debugInfo.innerHTML = Object.keys(info).map(key =>
                `<div><strong>${key}:</strong> ${JSON.stringify(info[key], null, 2)}</div>`
            ).join('');
        }

        // 页面错误处理
        window.onerror = function (msg, url, line, col, error) {
            console.error('页面错误:', msg, url, line, col, error);
            showError('页面运行异常，请刷新重试');
        };
    </script>
</body>

</html>